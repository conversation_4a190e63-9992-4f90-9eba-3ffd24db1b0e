import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/utils/render_box_util.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/events.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/page_status_provider.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/task_settle/state.dart';
import 'package:jojo_flutter_plan_pkg/static/audio.dart';
import 'package:jojo_flutter_plan_pkg/static/spine.dart';

import '../../../../../utils/file_util.dart';
import 'animation_audio_mixin.dart';

/// 负责执行奖章类型任务结算的动画
/// 这里面有三个spine动效， 1和2同时只存在一个
/// 1. 奖章升级动效，有一个in
/// 2. 奖章获得动效，有一个in和一个loop动画
/// 3. 奖章增加动效，有一个in动画
/// case1: 在奖章获得流程时，播放2动效的in和loop,loop播放时停留1.5秒开始执行飞行动画
/// case2: 在奖章升级流程时，在图片的后面播放2动效的loop,延迟500毫秒在图片上面播放1动效in, in动效结束后停留1.5秒开始执行飞行动画
class MedalAnimationWidget extends StatefulWidget {
  /// 奖章奖励
  final FinishCourseTaskSettleItemReward medalReward;

  /// 奖章资产widget的key
  final GlobalKey medalAssetKey;

  /// 结算动画类型
  final FinishCourseTaskSettleAnimationType animationType;

  /// 动画结束
  final Function() onAnimationEnd;

  const MedalAnimationWidget({
    super.key,
    required this.medalReward,
    required this.medalAssetKey,
    required this.animationType,
    required this.onAnimationEnd,
  });

  @override
  State<MedalAnimationWidget> createState() => _MedalAnimationWidgetState();
}

class _MedalAnimationWidgetState extends State<MedalAnimationWidget>
    with TickerProviderStateMixin, AnimationAudioPlayMixin, PageStatusProvider {
  ///日志
  final String _logTag = 'MedalAnimationWidget';

  // 奖章获得动效控制器
  final _gotSpineController = JoJoSpineAnimationController();
  final _gotSpineKey = UniqueKey();
  String _gotSpineAnimationName = 'in';
  bool _showGotSpine = false;

  // 奖章升级动效控制器
  final _upgradeSpineController = JoJoSpineAnimationController();
  final _upgradeSpineKey = UniqueKey();
  bool _showUpgradeSpine = false;

  final _medalSpineController = JoJoSpineAnimationController();
  final _medalSpineKey = GlobalKey();
  final _medalSpineLevelUpAnimationname = 'levelup';
  final _medalSpineLoopAnimationname = 'loop';
  bool _showMedalSpine = false;


  /// 奖章缩放动画
  late final AnimationController _scaleController;
  late final Animation<double> _scaleAnimation;

  /// 飞行动画相关
  late final AnimationController _flyController;
  late final Animation<double> _shrinkAnimation;
  late final Animation<double> _rotateAnimation;
  bool _isFlying = false;

  /// 呼吸动画相关
  late final AnimationController _breathingController;
  late final Animation<double> _breathingAnimation;

  /// 奖章增加动效控制器
  final _addSpineController = JoJoSpineAnimationController();
  final _addSpineKey = UniqueKey();
  bool _showAddSpine = false;

  /// 音频播放器
  final _medalAudioPlayer = AudioPlayer();

  /// 背景颜色
  Color _bgColor = HexColor('000000', 0.6);

  /// 奖章动效中的动画名字
  final String _medalAnimationInName = 'in';
  final String _medalAnimationLoopName = 'loop';

  /// 音频播放器
  final _audioPlayer = AudioPlayer();

  /// 奖章图片
  String? _medalImg;

  /// 奖章资产位置
  late Offset _medalAssetOffset;

  /// 动效停留时间，1.5 秒，这期间用户点击则直接执行飞行动效
  Timer? _spineStayTimer;

  /// 是否退后台
  bool _inBackground = false;

  /// 奖章图片宽度
  double medalImgWidth = taskSettleIsPad ? 450.rdp : 256.rdp;

  /// 初始化数据
  _initInfo() {
    _medalAssetOffset = getWidgetOffset(widget.medalAssetKey);
    _gotSpineAnimationName =
        (widget.animationType == FinishCourseTaskSettleAnimationType.medalGot)
            ? _medalAnimationInName
            : _medalAnimationLoopName;
    _medalImg = widget.medalReward.medalImgLocalPath;
    final directory = widget.medalReward.medalSpineResLocalPath ?? '';
    _showMedalSpine = directory.isNotEmpty && (Directory(directory).existsSync() && _hasValidSpine(directory));
    _showGotSpine = _showMedalSpine;
  }

    /// 检查指定目录中是否存在有效的 spine 资源文件
  bool _hasValidSpine(String directory) {
    File? atlasFile = findFilesByExtension(directory, "atlas.txt");
    File? skelTextFile = findFilesByExtension(directory, "skel.txt");
    File? skelBytesFile = findFilesByExtension(directory, "skel.bytes");
    return atlasFile != null && (skelTextFile != null || skelBytesFile != null);
  }

  @override
  handlePause(PagePauseEvent event) {
    l.i(_logTag, '退后台暂停播放音频');
    _medalAudioPlayer.pause();
    _audioPlayer.pause();
    _inBackground = true;
  }

  @override
  handleResume(PageResumeEvent event) {
    _inBackground = false;
    _medalAudioPlayer.resume();
  }

  @override
  void initState() {
    super.initState();
    initPageStatus();
    _initInfo();
    _initScaleAnimation();
    _initFlyAnimation();
    _initBreathingAnimation();
  }

  @override
  void dispose() {
    disposePageStatus();
    _scaleController.dispose();
    _flyController.dispose();
    _breathingController.dispose();
    _gotSpineController.dispose();
    _upgradeSpineController.dispose();
    _addSpineController.dispose();
    _medalSpineController.dispose();
    _medalAudioPlayer.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final upgradeBgAtla = _showMedalSpine
        ? AssetsSpine.SPINE_FINISH_COURSE_MEDAL_TX_LIGHT_LEVELUP_ATLAS
        : AssetsSpine.SPINE_FINISH_COURSE_MEDAL_UPGRADE_ATLAS;
    final upgradeBgSkel = _showMedalSpine
        ? AssetsSpine.SPINE_FINISH_COURSE_MEDAL_TX_LIGHT_LEVELUP_SKEL : AssetsSpine.SPINE_FINISH_COURSE_MEDAL_UPGRADE_SKEL;
l.d(tag, message)
    return GestureDetector(
      onTap: () => _tapView(),
      child: Builder(builder: (context) {
        return AnimatedContainer(
          // 修改为AnimatedContainer
          duration: const Duration(milliseconds: 500),
          color: _bgColor,
          child: Stack(children: [
            /// 奖章展示动效
            if (_showGotSpine)
              Center(
                child: SizedBox(
                  width: 800.rdp * taskSettleScale,
                  height: 500.rdp * taskSettleScale,
                  child: AnimatedBuilder(
                    animation: _scaleAnimation,
                    builder: (context, child) => Transform.scale(
                      scale: _scaleAnimation.value,
                      child: child,
                    ),
                    child: JoJoSpineAnimationWidget(
                      AssetsSpine.SPINE_FINISH_COURSE_MEDAL_GOT_ATLAS,
                      AssetsSpine.SPINE_FINISH_COURSE_MEDAL_GOT_SKEL,
                      LoadMode.assets,
                      _gotSpineController,
                      package: Config.package,
                      key: _gotSpineKey,
                      onInitialized: (controller) {
                        controller.skeleton.setScaleX(1.2);
                        controller.skeleton.setScaleY(1.2);
                        try {
                          _playGotSpine();
                        } catch (e) {
                          l.e(_logTag, "奖章获得动效播放失败");
                          _startStayTimer();
                        }
                      },
                    ),
                  ),
                ),
              ),

            if (_showMedalSpine)
              _buildMedalSpineStack()
            else
              _buildMedalImgStack(_isFlying),

            /// 奖章升级动效
            if (_showUpgradeSpine)
              Center(
                child: SizedBox(
                  width: 800.rdp * taskSettleScale,
                  height: 500.rdp * taskSettleScale,
                  child: JoJoSpineAnimationWidget(
                    upgradeBgAtla,
                    upgradeBgSkel,
                    LoadMode.assets,
                    _upgradeSpineController,
                    package: Config.package,
                    key: _upgradeSpineKey,
                    onInitialized: (controller) {
                      controller.skeleton.setScaleX(1.2);
                      controller.skeleton.setScaleY(1.2);
                      try {
                        _playUpgradeSpine();
                      } catch (e) {
                        l.e(_logTag, "奖章升级动效播放失败");
                        _startStayTimer();
                      }
                    },
                  ),
                ),
              ),

            /// 奖章增加动效
            if (_showAddSpine)
              Positioned(
                top: -60.rdp * taskSettleScale,
                left: _medalAssetOffset.dx - 80.rdp * taskSettleScale,
                child: SizedBox(
                  width: 200.rdp * taskSettleScale,
                  height: 200.rdp * taskSettleScale,
                  child: JoJoSpineAnimationWidget(
                    AssetsSpine.SPINE_FINISH_COURSE_ASSET_ADD_ATLAS,
                    AssetsSpine.SPINE_FINISH_COURSE_ASSET_ADD_SKEL,
                    LoadMode.assets,
                    _addSpineController,
                    package: Config.package,
                    key: _addSpineKey,
                    onInitialized: (controller) {
                      try {
                        _playAddSpine();
                      } catch (e) {
                        l.e(_logTag, "奖章增加动效播放失败");
                        _startStayTimer();
                      }
                    },
                  ),
                ),
              )
          ]),
        );
      }),
    );
  }

  Widget _buildMedalSpineStack() {
    final directory = widget.medalReward.medalSpineResLocalPath ?? '';
    File? atlasFile = findFilesByExtension(directory, "atlas.txt");
    File? skelFile = findFilesByExtension(directory, "skel.bytes");

  final a = AssetsSpine.SPINE_FINISH_COURSE_MEDAL_YI_ZHI_LV_2_ATLAS;
  final b =  AssetsSpine.SPINE_FINISH_COURSE_MEDAL_YI_ZHI_LV_2_SKEL;
  
    return AnimatedBuilder(
      animation: Listenable.merge([
        _flyController,
        _rotateAnimation,
        _shrinkAnimation,
        _scaleAnimation,
        _breathingAnimation,
      ]),
      builder: (context, child) {
        final screenSize = MediaQuery.of(context).size;

        final width = 800.rdp * taskSettleScale;
        final height = 500.rdp * taskSettleScale;

        final spine = SizedBox(
          width: width,
          height: height,
          child: JoJoSpineAnimationWidget(
            // atlasFile?.path ?? '',
            // skelFile?.path ?? '',
            a, b,
            LoadMode.assets,
            _medalSpineController,
            package: Config.package,
            key: _medalSpineKey,
            onInitialized: (controller) {
              controller.skeleton.setScaleX(0.5);
              controller.skeleton.setScaleY(0.5);
              try {
                _playMedalSpine(widget.animationType ==
                    FinishCourseTaskSettleAnimationType.medalUpgrade);
              } catch (e) {
                l.e(_logTag, "配置奖章动效播放失败");
              }
            },
          ),
        );
  
        if (_isFlying) {
          final startOffset =
              Offset(screenSize.width / 2, screenSize.height / 2);
          final targetOffset = Offset(
            _medalAssetOffset.dx + 40.rdp * taskSettleScale,
            _medalAssetOffset.dy + 15.rdp * taskSettleScale,
          );
          final currentOffset =
              Offset.lerp(startOffset, targetOffset, _flyController.value)!;

          return Positioned(
            left: currentOffset.dx - width / 2.0,
            top: currentOffset.dy - height / 2.0,
            child: Transform.rotate(
              angle: _rotateAnimation.value * (pi / 180),
              child: Transform.scale(
                scale: _shrinkAnimation.value,
                child: spine,
              ),
            ),
          );
        } else {
          double scaleValue = 1.0;
          if (_scaleController.status != AnimationStatus.dismissed) {
            // 缩放动画进行中
            scaleValue = _scaleAnimation.value;
          } else {
            // 显示呼吸动画
            scaleValue = _breathingAnimation.value;
          }

          return Center(
            child: Transform.scale(
              scale: scaleValue,
              child: spine,
            ),
          );
        }
      },
    );
  }

  Widget _buildMedalImgStack(bool isFlying) {
    if (isFlying) {
          /// 奖章图片飞行动画
      return AnimatedBuilder(
        animation: _flyController,
        builder: (context, child) {
          final screenSize = MediaQuery.of(context).size;
          final startOffset =
              Offset(screenSize.width / 2, screenSize.height / 2);
          final currentOffset = Offset.lerp(
            startOffset,
            Offset(_medalAssetOffset.dx + 40.rdp * taskSettleScale,
                _medalAssetOffset.dy + 15.rdp * taskSettleScale),
            _flyController.value,
          )!;

          return Positioned(
            left: currentOffset.dx - medalImgWidth / 2.0,
            top: currentOffset.dy - medalImgWidth / 2.0,
            child: Transform.rotate(
              angle: _rotateAnimation.value * (pi / 180),
              child: Transform.scale(
                scale: _shrinkAnimation.value,
                child: Image.file(
                  File(_medalImg ?? ''),
                  width: medalImgWidth,
                  height: medalImgWidth,
                  cacheWidth: medalImgWidth.ceil() *
                      MediaQuery.of(context).devicePixelRatio.toInt(),
                  cacheHeight: medalImgWidth.ceil() *
                      MediaQuery.of(context).devicePixelRatio.toInt(),
                  fit: BoxFit.contain,
                ),
              ),
            ),
          );
        },
      );
    } else {
      /// 奖章图片
      return Center(
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) => Transform.scale(
            scale: _scaleAnimation.value,
            child: child,
          ),
          child: Image.file(
            File(_medalImg ?? ''),
            width: medalImgWidth,
            height: medalImgWidth,
            cacheWidth: medalImgWidth.toInt(),
            cacheHeight: medalImgWidth.toInt(),
            fit: BoxFit.contain,
          ),
        ),
      );
    }
  }

  void _initBreathingAnimation() {
    _breathingController = AnimationController(
      duration: const Duration(milliseconds: 1000), // 总时长1秒
      vsync: this,
    );

    _breathingAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.03),
        weight: 50, // 前半段：1→1.03
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.03, end: 1.0),
        weight: 50, // 后半段：1.03→1
      ),
    ]).animate(CurvedAnimation(
      parent: _breathingController,
      curve: Curves.linear,
    ));
  }

  
  /// 初始化缩放动画
  void _initScaleAnimation() {
    final duration = _showMedalSpine ? 300: 500;
    final firstScale = _showMedalSpine ? 0.5 : 1.0;
    final secondScale = _showMedalSpine ? 1.1 : 1.6;
    final double firstWeight = _showMedalSpine ? 50 : 70;
    final double secondWeight = _showMedalSpine ? 50 : 30;

    _scaleController = AnimationController(
      duration: Duration(milliseconds: duration),
      vsync: this,
    );

    _scaleAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: firstScale, end: secondScale),
        weight: firstWeight,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: secondScale, end: 1.0),
        weight: secondWeight,
      ),
    ]).animate(_scaleController);

    _scaleController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _scaleController.reset();
        setState(() => _showGotSpine = true);
        _breathingController.repeat();
        _playMedalIntroAudio();
      }
    });

    _scaleController.forward();
  }

  /// 初始化飞行动画
  void _initFlyAnimation() {
    _flyController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _shrinkAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _flyController, curve: Curves.linear),
    );

    _rotateAnimation = Tween<double>(begin: 0.0, end: 60.0).animate(
      CurvedAnimation(parent: _flyController, curve: Curves.linear),
    );

    _flyController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        l.d(_logTag, "奖章飞行动画完成");
      }
    });
  }

  /// 播放奖章介绍音效
  _playMedalIntroAudio() async {
    if(_inBackground) return;
    final path = widget.medalReward.medalAudioLocalPath;
    if (path?.isNotEmpty ?? false) {
      try {
        await _medalAudioPlayer.play(DeviceFileSource(path!));
      } catch (e) {
        l.e(_logTag, "奖章音效播放失败: ${e.toString()}");
      }
    } else {
      l.i(_logTag, "无奖章音效，可能还没下载完成");
    }
  }

  /// 播放奖章显示音效
  _playMedalShowAudio() {
    if(_inBackground) return;
    playAudio(audioPlayer: _audioPlayer, path: AssetsAudio.FINISH_COURSE_MEDAL_SHOW);
  }

  /// 播放奖章升级音效
  _playMedalUpgradeAudio() {
    if(_inBackground) return;
    playAudio(audioPlayer: _audioPlayer, path: AssetsAudio.FINISH_COURSE_MEDAL_UPGRADE);
  }

  /// 播放奖章飞行音效
  _playMedalFlyAudio() {
    if(_inBackground) return;
    playAudio(audioPlayer: _audioPlayer, path: AssetsAudio.FINISH_COURSE_MEDAL_FLY);
  }

  /// 开始飞行动画
  _startFlyAnimation() {
    _cancelTimer();
    if (_medalAssetOffset != Offset.zero && mounted) {
      l.i(_logTag, '开始奖章飞行动画');
      setState(() {
        _isFlying = true;
        _showGotSpine = false;
        _showUpgradeSpine = false;
        _showAddSpine = true;
        _bgColor = Colors.transparent;
      });
      _breathingController.stop();
      _breathingController.reset();
      _flyController.forward();
      _playMedalFlyAudio();
    }
  }

  /// 播放奖章获得动画
  _playGotSpine() {
    l.i(_logTag, '播放奖章动效：${widget.animationType} 名字：$_gotSpineAnimationName');
    _gotSpineController.playAnimation(
      JoJoSpineAnimation(
          animaitonName: _gotSpineAnimationName,
          trackIndex: 0,
          loop: (_gotSpineAnimationName == _medalAnimationLoopName),
          delay: 0,
          listener: _gotSpineAnimationEvent),
    );
    if(widget.animationType == FinishCourseTaskSettleAnimationType.medalGot) {
      _playMedalShowAudio();
    } else {
      _playMedalUpgradeAudio();
    }
    
    if (widget.animationType == FinishCourseTaskSettleAnimationType.medalGot &&
        _gotSpineAnimationName == _medalAnimationLoopName) {
      // 奖章获得loop动画播放时启动计时器，1.5s后结束动画开启飞行动画
      _startStayTimer();
    }
  }

  /// 播放奖章升级动效
  _playUpgradeSpine() {
    l.i(_logTag, '开始奖章升级动效');
    _upgradeSpineController.playAnimation(
      JoJoSpineAnimation(
          animaitonName: _medalAnimationInName,
          trackIndex: 0,
          loop: false,
          delay: 0,
          listener: _upgradeSpineAnimationEvent),
    );
  }

    /// 奖章增加动效播放
  _playAddSpine() {
    l.i(_logTag, '开始奖章增加动效');
    _addSpineController.playAnimation(
      JoJoSpineAnimation(
          animaitonName: 'in',
          trackIndex: 0,
          loop: false,
          delay: 0,
          listener: _addSpineAnimationEvent),
    );
  }

  _playMedalSpine(bool isLevelUpgrade) {
    l.i(_logTag, '开始配置的奖章动效果 isLevelUpgrade: $isLevelUpgrade');
    final animationName = isLevelUpgrade ? _medalSpineLevelUpAnimationname : _medalSpineLoopAnimationname;
    var joJoSpineAnimation = JoJoSpineAnimation(
        animaitonName: animationName, trackIndex: 0, loop: !isLevelUpgrade, listener: (type) {
          if (AnimationEventType.complete == type) {
            _playMedalSpine(false);
          }
        });
    _medalSpineController.playAnimation(joJoSpineAnimation);
  }

  /// 点击视图
  _tapView() {
    if (_spineStayTimer != null) {
      l.i(_logTag, "点击页面跳过停留timer");
      _startFlyAnimation();
    }
  }

  /// 开发奖章停留计时
  _startStayTimer() {
    l.i(_logTag, "开启动效停留timer");
    _cancelTimer();
    _spineStayTimer = Timer(const Duration(milliseconds: 1500), () {
      _startFlyAnimation();
    });
  }

  /// 取消 Timer
  _cancelTimer() {
    l.i(_logTag, "取消动效停留timer");
    _spineStayTimer?.cancel();
    _spineStayTimer = null;
  }

  /// 奖章获得动效动画完成回调
  _gotSpineAnimationEvent(AnimationEventType type) {
    if(widget.animationType ==
              FinishCourseTaskSettleAnimationType.medalUpgrade) {
      if (AnimationEventType.start == type) {
        // 奖章升级流程中，奖章获得动效开始播放时，200毫秒后开始播放升级动效
        final duration = _showMedalSpine ? 500 : 200;
        Future.delayed(Duration(milliseconds: duration), () {
          setState(() {
            _showUpgradeSpine = true;
          });
        });
      }
      return;
    }
    if (AnimationEventType.complete == type && _gotSpineAnimationName == _medalAnimationInName) {
        // 奖章获得动效in动画播放后播放loop
        _gotSpineAnimationName = _medalAnimationLoopName;
        _playGotSpine();
    }
  }

  /// 奖章升级动效动画完成回调
  _upgradeSpineAnimationEvent(AnimationEventType type) {
    if (AnimationEventType.start == type) {
        // 奖章升级动效开始播放时，1000毫秒后切换为升级图片
        Future.delayed(const Duration(milliseconds: 1000), () {
          setState(() {
            _medalImg = widget.medalReward.medalUpgradeImgLocalPath;
          });
        });
    } else if(AnimationEventType.complete == type) {
      // 奖章升级动效始播放完成时，开启1.5s保持计时
      _startStayTimer();
    }
  }

  /// 奖章增加动效动画完成回调
  _addSpineAnimationEvent(AnimationEventType type) {
    if (AnimationEventType.complete == type) {
      widget.onAnimationEnd();
    }
  }
}
